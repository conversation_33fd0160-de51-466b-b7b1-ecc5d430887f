---
title: "Simple Search"
api: "POST https://api.browser-use.com/api/v1/simple-search"
description: "Search Google and extract relevant content from multiple top results"
---

## Overview

Search and extract content from multiple websites in real-time. Gets live data by actually visiting sites, not cached results.

💡 **Complete working example**: [simple_search.py](https://github.com/browser-use/browser-use/blob/main/examples/search/simple_search.py)

## Request

<ParamField body="query" type="string" required>
  The search query to process
</ParamField>

<ParamField body="max_websites" type="integer" default="5">
  Maximum number of websites to process from search results (1-10)
</ParamField>

<ParamField body="depth" type="integer" default="2">
  How deep to navigate within each website (2-5). Higher depth = more thorough exploration through multiple page clicks.
</ParamField>

## Response

<ResponseField name="results" type="array">
  Array of results from processed websites
  
  <Expandable title="result object">
    <ResponseField name="url" type="string">
      The URL of the processed website
    </ResponseField>
    
    <ResponseField name="content" type="string">
      Extracted content relevant to the search query
    </ResponseField>
  </Expandable>
</ResponseField>

<ResponseExample>
```json Response
{
  "results": [
    {
      "url": "https://example1.com",
      "content": "Relevant content extracted from the first website..."
    },
    {
      "url": "https://example2.com", 
      "content": "Relevant content extracted from the second website..."
    }
  ]
}
```
</ResponseExample>

<RequestExample>
```python Python
import aiohttp
import asyncio

async def search():
    payload = {
        "query": "latest developments in artificial intelligence",
        "max_websites": 5,
        "depth": 2
    }
    
    headers = {
        "Authorization": "Bearer YOUR_API_KEY",
        "Content-Type": "application/json"
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(
            "https://api.browser-use.com/api/v1/simple-search",
            json=payload,
            headers=headers
        ) as response:
            return await response.json()

result = asyncio.run(search())
print(result)
```

```javascript JavaScript
const response = await fetch('https://api.browser-use.com/api/v1/simple-search', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    query: 'latest developments in artificial intelligence',
    max_websites: 5,
    depth: 2
  })
});

const result = await response.json();
console.log(result);
```

```bash cURL
curl -X POST "https://api.browser-use.com/api/v1/simple-search" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "latest developments in artificial intelligence",
    "max_websites": 5,
    "depth": 2
  }'
```
</RequestExample>

## Pricing

**Cost per request**: `1 cent × depth × max_websites`

Examples:
- depth=2, max_websites=5 = 10 cents per request (default values)
- depth=2, max_websites=3 = 6 cents per request
- depth=3, max_websites=2 = 6 cents per request 
