// 控制台 DOM 分析器 - 可直接在浏览器控制台执行
function analyzeDOMNodes(options = {}) {
  const {
    includeHidden = false,        // 是否包含隐藏元素
    includeNonInteractive = true, // 是否包含非交互元素
    maxDepth = 10,               // 最大遍历深度
    startElement = document.body  // 起始元素
  } = options;

  const results = [];
  const processedNodes = new WeakSet();

  // 缓存系统
  const cache = {
    styles: new WeakMap(),
    rects: new WeakMap()
  };

  // 获取缓存的样式
  function getCachedStyle(element) {
    if (cache.styles.has(element)) {
      return cache.styles.get(element);
    }
    const style = window.getComputedStyle(element);
    cache.styles.set(element, style);
    return style;
  }

  // 获取缓存的位置信息
  function getCachedRect(element) {
    if (cache.rects.has(element)) {
      return cache.rects.get(element);
    }
    const rect = element.getBoundingClientRect();
    cache.rects.set(element, rect);
    return rect;
  }

  // 检查元素可见性
  function isVisible(element) {
    if (!element || element.nodeType !== Node.ELEMENT_NODE) return false;
    
    const style = getCachedStyle(element);
    const rect = getCachedRect(element);
    
    return (
      rect.width > 0 &&
      rect.height > 0 &&
      style.display !== 'none' &&
      style.visibility !== 'hidden' &&
      style.opacity !== '0'
    );
  }

  // 检查元素交互性
  function isInteractive(element) {
    if (!element || element.nodeType !== Node.ELEMENT_NODE) return false;

    const tagName = element.tagName.toLowerCase();
    
    // 原生交互元素
    const interactiveTags = new Set([
      'a', 'button', 'input', 'select', 'textarea', 'details', 'summary'
    ]);
    
    if (interactiveTags.has(tagName)) return true;

    // 检查属性
    const interactiveAttrs = [
      'onclick', 'onmousedown', 'onmouseup', 'onkeydown', 'onkeyup',
      'tabindex', 'contenteditable', 'draggable'
    ];
    
    for (const attr of interactiveAttrs) {
      if (element.hasAttribute(attr) || element[attr]) return true;
    }

    // 检查角色
    const role = element.getAttribute('role');
    const interactiveRoles = new Set([
      'button', 'link', 'menuitem', 'tab', 'checkbox', 'radio'
    ]);
    
    if (role && interactiveRoles.has(role)) return true;

    // 检查样式
    const style = getCachedStyle(element);
    if (style.cursor === 'pointer') return true;

    return false;
  }

  // 生成简化的 XPath
  function getSimpleXPath(element) {
    if (element === document.body) return '/body';
    
    const parts = [];
    let current = element;
    
    while (current && current !== document.body && current.parentElement) {
      const tagName = current.tagName.toLowerCase();
      const siblings = Array.from(current.parentElement.children)
        .filter(el => el.tagName.toLowerCase() === tagName);
      
      if (siblings.length === 1) {
        parts.unshift(tagName);
      } else {
        const index = siblings.indexOf(current) + 1;
        parts.unshift(`${tagName}[${index}]`);
      }
      
      current = current.parentElement;
    }
    
    return '/body/' + parts.join('/');
  }

  // 获取元素属性
  function getElementAttributes(element) {
    const attrs = {};
    for (const attr of element.attributes) {
      attrs[attr.name] = attr.value;
    }
    return attrs;
  }

  // 递归分析节点
  function analyzeNode(node, depth = 0) {
    if (depth > maxDepth || processedNodes.has(node)) return;
    
    processedNodes.add(node);

    // 处理元素节点
    if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node;
      const visible = isVisible(element);
      const interactive = isInteractive(element);
      
      // 根据选项过滤
      if (!includeHidden && !visible) return;
      if (!includeNonInteractive && !interactive) return;

      const rect = getCachedRect(element);
      const style = getCachedStyle(element);
      
      const nodeInfo = {
        tagName: element.tagName.toLowerCase(),
        xpath: getSimpleXPath(element),
        attributes: getElementAttributes(element),
        text: element.textContent?.trim().substring(0, 100) || '',
        isVisible: visible,
        isInteractive: interactive,
        position: {
          x: Math.round(rect.x),
          y: Math.round(rect.y),
          width: Math.round(rect.width),
          height: Math.round(rect.height)
        },
        styles: {
          display: style.display,
          visibility: style.visibility,
          opacity: style.opacity,
          cursor: style.cursor
        },
        depth: depth
      };

      results.push(nodeInfo);
    }
    
    // 处理文本节点
    else if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent?.trim();
      if (text && text.length > 0) {
        results.push({
          type: 'text',
          text: text.substring(0, 100),
          parent: node.parentElement?.tagName.toLowerCase() || 'unknown',
          depth: depth
        });
      }
    }

    // 递归处理子节点
    for (const child of node.childNodes) {
      analyzeNode(child, depth + 1);
    }
  }

  // 开始分析
  console.time('DOM Analysis');
  analyzeNode(startElement);
  console.timeEnd('DOM Analysis');

  // 返回结果和统计信息
  const stats = {
    totalNodes: results.length,
    visibleNodes: results.filter(n => n.isVisible).length,
    interactiveNodes: results.filter(n => n.isInteractive).length,
    textNodes: results.filter(n => n.type === 'text').length
  };

  return {
    nodes: results,
    stats: stats,
    // 辅助方法
    getByTag: (tag) => results.filter(n => n.tagName === tag),
    getInteractive: () => results.filter(n => n.isInteractive),
    getVisible: () => results.filter(n => n.isVisible),
    getByXPath: (xpath) => results.find(n => n.xpath === xpath)
  };
}

// 使用示例和快捷方法
window.domAnalyzer = {
  // 基础分析
  analyze: analyzeDOMNodes,

  // 快速获取所有交互元素
  getInteractive: () => analyzeDOMNodes({ includeNonInteractive: false }),

  // 获取可见元素
  getVisible: () => analyzeDOMNodes({ includeHidden: false }),

  // 分析特定元素
  analyzeElement: (selector) => {
    const element = document.querySelector(selector);
    if (!element) {
      console.error('Element not found:', selector);
      return null;
    }
    return analyzeDOMNodes({ startElement: element, maxDepth: 5 });
  },

  // 高亮交互元素（可选功能）
  highlightInteractive: () => {
    // 先清除之前的高亮
    window.domAnalyzer.clearHighlights();

    const result = analyzeDOMNodes({ includeNonInteractive: false });
    console.log('Found interactive elements:', result.nodes.length);

    let highlightedCount = 0;
    result.nodes.forEach((node, index) => {
      try {
        // 尝试多种方式找到元素
        let element = null;

        // 方法1: 使用 XPath
        try {
          element = document.evaluate(
            node.xpath,
            document,
            null,
            XPathResult.FIRST_ORDERED_NODE_TYPE,
            null
          ).singleNodeValue;
        } catch (e) {
          console.warn('XPath failed for:', node.xpath, e);
        }

        // 方法2: 如果有 id，直接查找
        if (!element && node.attributes && node.attributes.id) {
          element = document.getElementById(node.attributes.id);
        }

        // 方法3: 如果有 class，尝试查找
        if (!element && node.attributes && node.attributes.class) {
          const candidates = document.querySelectorAll(`.${node.attributes.class.split(' ')[0]}`);
          for (const candidate of candidates) {
            if (candidate.tagName.toLowerCase() === node.tagName) {
              element = candidate;
              break;
            }
          }
        }

        if (element) {
          // 添加高亮样式
          element.style.outline = '3px solid #ff0000';
          element.style.outlineOffset = '2px';
          element.style.position = element.style.position || 'relative';
          element.setAttribute('data-dom-analyzer-highlighted', 'true');

          // 创建标签
          const label = document.createElement('div');
          label.textContent = index;
          label.className = 'dom-analyzer-label';
          label.style.cssText = `
            position: absolute !important;
            top: -25px !important;
            left: -2px !important;
            background: #ff0000 !important;
            color: white !important;
            padding: 4px 8px !important;
            font-size: 12px !important;
            font-weight: bold !important;
            z-index: 999999 !important;
            pointer-events: none !important;
            border-radius: 3px !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
          `;

          // 确保标签不会被其他元素遮挡
          element.style.zIndex = Math.max(parseInt(element.style.zIndex) || 0, 999998);
          element.appendChild(label);

          highlightedCount++;
          console.log(`Highlighted element ${index}:`, node.tagName, node.xpath);
        } else {
          console.warn(`Could not find element for:`, node);
        }
      } catch (e) {
        console.error(`Error highlighting element ${index}:`, e, node);
      }
    });

    console.log(`✅ Successfully highlighted ${highlightedCount}/${result.nodes.length} interactive elements`);

    // 滚动到第一个高亮元素
    if (highlightedCount > 0) {
      const firstHighlighted = document.querySelector('[data-dom-analyzer-highlighted="true"]');
      if (firstHighlighted) {
        firstHighlighted.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }

    return result;
  },

  // 清除高亮
  clearHighlights: () => {
    // 清除所有高亮元素
    document.querySelectorAll('[data-dom-analyzer-highlighted="true"]').forEach(el => {
      el.style.outline = '';
      el.style.outlineOffset = '';
      el.removeAttribute('data-dom-analyzer-highlighted');

      // 移除标签
      const labels = el.querySelectorAll('.dom-analyzer-label');
      labels.forEach(label => label.remove());
    });

    // 备用清理方法
    document.querySelectorAll('.dom-analyzer-label').forEach(label => label.remove());

    console.log('✅ All highlights cleared');
  },

  // 调试方法：显示找到的交互元素信息
  debugInteractive: () => {
    const result = analyzeDOMNodes({ includeNonInteractive: false });
    console.log('=== 交互元素调试信息 ===');
    console.log(`总共找到 ${result.nodes.length} 个交互元素:`);

    result.nodes.forEach((node, index) => {
      console.log(`${index}. ${node.tagName}`, {
        xpath: node.xpath,
        attributes: node.attributes,
        text: node.text.substring(0, 50),
        position: node.position,
        isVisible: node.isVisible
      });
    });

    return result;
  },

  // 简单高亮方法（用于调试）
  simpleHighlight: () => {
    // 直接查找常见的交互元素并高亮
    const selectors = [
      'button', 'a[href]', 'input[type="button"]', 'input[type="submit"]',
      '[onclick]', '[role="button"]', 'select', 'textarea', 'input[type="text"]'
    ];

    let count = 0;
    selectors.forEach(selector => {
      document.querySelectorAll(selector).forEach((el, index) => {
        if (el.offsetWidth > 0 && el.offsetHeight > 0) {
          el.style.outline = '2px solid blue';
          el.style.backgroundColor = 'rgba(0, 255, 255, 0.1)';
          count++;
        }
      });
    });

    console.log(`简单高亮了 ${count} 个元素`);
    return count;
  }
};

console.log('🔍 DOM Analyzer loaded! Usage:');
console.log('• domAnalyzer.analyze() - 完整分析');
console.log('• domAnalyzer.getInteractive() - 获取交互元素');
console.log('• domAnalyzer.getVisible() - 获取可见元素');
console.log('• domAnalyzer.analyzeElement("selector") - 分析特定元素');
console.log('• domAnalyzer.highlightInteractive() - 高亮交互元素');
console.log('• domAnalyzer.clearHighlights() - 清除高亮');
