// 控制台 DOM 分析器 - 可直接在浏览器控制台执行
function analyzeDOMNodes(options = {}) {
  const {
    includeHidden = false,        // 是否包含隐藏元素
    includeNonInteractive = true, // 是否包含非交互元素
    maxDepth = 10,               // 最大遍历深度
    startElement = document.body  // 起始元素
  } = options;

  const results = [];
  const processedNodes = new WeakSet();

  // 缓存系统
  const cache = {
    styles: new WeakMap(),
    rects: new WeakMap()
  };

  // 获取缓存的样式
  function getCachedStyle(element) {
    if (cache.styles.has(element)) {
      return cache.styles.get(element);
    }
    const style = window.getComputedStyle(element);
    cache.styles.set(element, style);
    return style;
  }

  // 获取缓存的位置信息
  function getCachedRect(element) {
    if (cache.rects.has(element)) {
      return cache.rects.get(element);
    }
    const rect = element.getBoundingClientRect();
    cache.rects.set(element, rect);
    return rect;
  }

  // 检查元素可见性
  function isVisible(element) {
    if (!element || element.nodeType !== Node.ELEMENT_NODE) return false;
    
    const style = getCachedStyle(element);
    const rect = getCachedRect(element);
    
    return (
      rect.width > 0 &&
      rect.height > 0 &&
      style.display !== 'none' &&
      style.visibility !== 'hidden' &&
      style.opacity !== '0'
    );
  }

  // 检查元素交互性
  function isInteractive(element) {
    if (!element || element.nodeType !== Node.ELEMENT_NODE) return false;

    const tagName = element.tagName.toLowerCase();
    
    // 原生交互元素
    const interactiveTags = new Set([
      'a', 'button', 'input', 'select', 'textarea', 'details', 'summary'
    ]);
    
    if (interactiveTags.has(tagName)) return true;

    // 检查属性
    const interactiveAttrs = [
      'onclick', 'onmousedown', 'onmouseup', 'onkeydown', 'onkeyup',
      'tabindex', 'contenteditable', 'draggable'
    ];
    
    for (const attr of interactiveAttrs) {
      if (element.hasAttribute(attr) || element[attr]) return true;
    }

    // 检查角色
    const role = element.getAttribute('role');
    const interactiveRoles = new Set([
      'button', 'link', 'menuitem', 'tab', 'checkbox', 'radio'
    ]);
    
    if (role && interactiveRoles.has(role)) return true;

    // 检查样式
    const style = getCachedStyle(element);
    if (style.cursor === 'pointer') return true;

    return false;
  }

  // 生成简化的 XPath
  function getSimpleXPath(element) {
    if (element === document.body) return '/body';
    
    const parts = [];
    let current = element;
    
    while (current && current !== document.body && current.parentElement) {
      const tagName = current.tagName.toLowerCase();
      const siblings = Array.from(current.parentElement.children)
        .filter(el => el.tagName.toLowerCase() === tagName);
      
      if (siblings.length === 1) {
        parts.unshift(tagName);
      } else {
        const index = siblings.indexOf(current) + 1;
        parts.unshift(`${tagName}[${index}]`);
      }
      
      current = current.parentElement;
    }
    
    return '/body/' + parts.join('/');
  }

  // 获取元素属性
  function getElementAttributes(element) {
    const attrs = {};
    for (const attr of element.attributes) {
      attrs[attr.name] = attr.value;
    }
    return attrs;
  }

  // 递归分析节点
  function analyzeNode(node, depth = 0) {
    if (depth > maxDepth || processedNodes.has(node)) return;
    
    processedNodes.add(node);

    // 处理元素节点
    if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node;
      const visible = isVisible(element);
      const interactive = isInteractive(element);
      
      // 根据选项过滤
      if (!includeHidden && !visible) return;
      if (!includeNonInteractive && !interactive) return;

      const rect = getCachedRect(element);
      const style = getCachedStyle(element);
      
      const nodeInfo = {
        tagName: element.tagName.toLowerCase(),
        xpath: getSimpleXPath(element),
        attributes: getElementAttributes(element),
        text: element.textContent?.trim().substring(0, 100) || '',
        isVisible: visible,
        isInteractive: interactive,
        position: {
          x: Math.round(rect.x),
          y: Math.round(rect.y),
          width: Math.round(rect.width),
          height: Math.round(rect.height)
        },
        styles: {
          display: style.display,
          visibility: style.visibility,
          opacity: style.opacity,
          cursor: style.cursor
        },
        depth: depth
      };

      results.push(nodeInfo);
    }
    
    // 处理文本节点
    else if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent?.trim();
      if (text && text.length > 0) {
        results.push({
          type: 'text',
          text: text.substring(0, 100),
          parent: node.parentElement?.tagName.toLowerCase() || 'unknown',
          depth: depth
        });
      }
    }

    // 递归处理子节点
    for (const child of node.childNodes) {
      analyzeNode(child, depth + 1);
    }
  }

  // 开始分析
  console.time('DOM Analysis');
  analyzeNode(startElement);
  console.timeEnd('DOM Analysis');

  // 返回结果和统计信息
  const stats = {
    totalNodes: results.length,
    visibleNodes: results.filter(n => n.isVisible).length,
    interactiveNodes: results.filter(n => n.isInteractive).length,
    textNodes: results.filter(n => n.type === 'text').length
  };

  return {
    nodes: results,
    stats: stats,
    // 辅助方法
    getByTag: (tag) => results.filter(n => n.tagName === tag),
    getInteractive: () => results.filter(n => n.isInteractive),
    getVisible: () => results.filter(n => n.isVisible),
    getByXPath: (xpath) => results.find(n => n.xpath === xpath)
  };
}

// 使用示例和快捷方法
window.domAnalyzer = {
  // 基础分析
  analyze: analyzeDOMNodes,

  // 快速获取所有交互元素
  getInteractive: () => analyzeDOMNodes({ includeNonInteractive: false }),

  // 获取可见元素
  getVisible: () => analyzeDOMNodes({ includeHidden: false }),

  // 分析特定元素
  analyzeElement: (selector) => {
    const element = document.querySelector(selector);
    if (!element) {
      console.error('Element not found:', selector);
      return null;
    }
    return analyzeDOMNodes({ startElement: element, maxDepth: 5 });
  },

  // 高亮交互元素（可选功能）
  highlightInteractive: () => {
    const result = analyzeDOMNodes({ includeNonInteractive: false });
    result.nodes.forEach((node, index) => {
      const element = document.evaluate(
        node.xpath,
        document,
        null,
        XPathResult.FIRST_ORDERED_NODE_TYPE,
        null
      ).singleNodeValue;

      if (element) {
        element.style.outline = '2px solid red';
        element.style.position = 'relative';

        const label = document.createElement('div');
        label.textContent = index;
        label.style.cssText = `
          position: absolute;
          top: -20px;
          left: 0;
          background: red;
          color: white;
          padding: 2px 6px;
          font-size: 12px;
          z-index: 10000;
          pointer-events: none;
        `;
        element.appendChild(label);
      }
    });

    console.log(`Highlighted ${result.nodes.length} interactive elements`);
    return result;
  },

  // 清除高亮
  clearHighlights: () => {
    document.querySelectorAll('[style*="outline: 2px solid red"]').forEach(el => {
      el.style.outline = '';
      const label = el.querySelector('div[style*="background: red"]');
      if (label) label.remove();
    });
    console.log('Highlights cleared');
  }
};

console.log('🔍 DOM Analyzer loaded! Usage:');
console.log('• domAnalyzer.analyze() - 完整分析');
console.log('• domAnalyzer.getInteractive() - 获取交互元素');
console.log('• domAnalyzer.getVisible() - 获取可见元素');
console.log('• domAnalyzer.analyzeElement("selector") - 分析特定元素');
console.log('• domAnalyzer.highlightInteractive() - 高亮交互元素');
console.log('• domAnalyzer.clearHighlights() - 清除高亮');
